import React from 'react';
import { Polygon, Circle } from 'react-leaflet';
import * as turf from '@turf/turf';
import { Unit } from '../types';
import { terrainFeatures, obstaclePolygons } from './TerrainAndObstacles';

// دالة للحصول على مركز الوحدة
export const getUnitCenter = (unit: Unit): [number, number] => {
  if (unit.location.type === 'polygon') {
    const coords = unit.location.coordinates as number[][][];
    if (coords && coords[0] && coords[0][0]) {
      const centerLat = coords[0].reduce((sum, point) => sum + point[0], 0) / coords[0].length;
      const centerLng = coords[0].reduce((sum, point) => sum + point[1], 0) / coords[0].length;
      return [centerLat, centerLng];
    }
  }
  return [35, 38]; // موقع افتراضي في سوريا
};

// دالة للحصول على مدى الدوائر للوحدة
export const getCircleRanges = (unit: Unit, equipmentData: any[], circleConfig: any) => {
  const eqs = equipmentData.filter(eq => eq.unitId === unit.id && eq.capabilities.includes('attack') && eq.status === 'operational');
  const mainType = eqs.length > 0 ? eqs[0].type : 'tank';
  const config = circleConfig || {};
  
  // مدى افتراضي محسن
  const defaultRanges = {
    tank: { blind: 3, kill: 8, nonkill: 15 },
    artillery: { blind: 5, kill: 15, nonkill: 25 },
    missile: { blind: 10, kill: 25, nonkill: 50 },
    anti_air: { blind: 8, kill: 20, nonkill: 35 },
    helicopter: { blind: 15, kill: 30, nonkill: 60 },
    fighter: { blind: 25, kill: 50, nonkill: 100 },
    apc: { blind: 2, kill: 5, nonkill: 10 },
    default: { blind: 3, kill: 8, nonkill: 15 }
  };
  
  return config[mainType] || defaultRanges[mainType] || defaultRanges.default;
};

// دالة محسنة لرسم الدوائر مع قطع التضاريس
export const renderCircleWithTerrain = (unit: Unit, equipmentData: any[], circleConfig: any) => {
  const [centerLat, centerLng] = getUnitCenter(unit);
  const { blind, kill, nonkill } = getCircleRanges(unit, equipmentData, circleConfig);
  
  const renderClippedByTerrain = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
    try {
      // إنشاء دائرة أساسية
      const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
      const polygonElements: JSX.Element[] = [];
      
      // قطع الدائرة بالتضاريس
      let remaining = circle;
      terrainFeatures.forEach((terrain, index) => {
        if (remaining && terrain.geometry.type === 'Polygon') {
          try {
            // تحديد تأثير التضاريس على الرؤية
            const elevationEffect = terrain.properties.elevation > 1000 ? 0.7 : 0.9; // الجبال العالية تحجب أكثر
            
            const terrainFeature = turf.feature(terrain.geometry);
            const intersection = turf.intersect(remaining, terrainFeature);
            
            if (intersection) {
              // إضافة منطقة متأثرة بالتضاريس
              const affectedCoords = intersection.geometry.type === 'Polygon' 
                ? [intersection.geometry.coordinates] 
                : intersection.geometry.coordinates;
              
              affectedCoords.forEach((coords, coordIndex) => {
                polygonElements.push(
                  <Polygon
                    key={`${keyPrefix}-terrain-affected-${unit.id}-${index}-${coordIndex}`}
                    positions={coords[0].map(coord => [coord[1], coord[0]])}
                    pathOptions={{
                      color: color,
                      fillColor: fillColor,
                      fillOpacity: 0.1 * elevationEffect, // تقليل الشفافية في المناطق المتأثرة
                      weight: 2,
                      dashArray: '5 5',
                      opacity: 0.6
                    }}
                  />
                );
              });
              
              // إزالة المنطقة المتأثرة من الدائرة الأساسية
              const difference = turf.difference(remaining, terrainFeature);
              if (difference) remaining = difference;
            }
          } catch (error) {
            console.warn('خطأ في معالجة التضاريس:', error);
          }
        }
      });
      
      // رسم الجزء المتبقي من الدائرة
      if (remaining && remaining.geometry.type === 'Polygon') {
        const coords = remaining.geometry.coordinates;
        coords.forEach((ring, ringIndex) => {
          polygonElements.push(
            <Polygon
              key={`${keyPrefix}-remaining-${unit.id}-${ringIndex}`}
              positions={ring.map(coord => [coord[1], coord[0]])}
              pathOptions={{
                color: color,
                fillColor: fillColor,
                fillOpacity: 0.2,
                weight: 3,
                opacity: 0.8
              }}
            />
          );
        });
      }
      
      return polygonElements;
    } catch (error) {
      console.warn('خطأ في رسم الدائرة مع التضاريس:', error);
      // العودة إلى دائرة عادية في حالة الخطأ
      return [
        <Circle
          key={`${keyPrefix}-fallback-${unit.id}`}
          center={[centerLat, centerLng]}
          radius={radius * 1000}
          pathOptions={{ color, fillColor, fillOpacity: 0.2, weight: 3 }}
        />
      ];
    }
  };

  return [
    ...renderClippedByTerrain(blind, '#059669', '#10b981', 'blind-terrain'),
    ...renderClippedByTerrain(kill, '#dc2626', '#ef4444', 'kill-terrain'),
    ...renderClippedByTerrain(nonkill, '#d97706', '#f59e0b', 'nonkill-terrain')
  ];
};

// دالة محسنة لرسم الدوائر مع قطع العوائق
export const renderCircleWithObstacles = (unit: Unit, equipmentData: any[], circleConfig: any) => {
  const [centerLat, centerLng] = getUnitCenter(unit);
  const { blind, kill, nonkill } = getCircleRanges(unit, equipmentData, circleConfig);
  
  const renderClippedByObstacles = (radius: number, color: string, fillColor: string, keyPrefix: string) => {
    try {
      // إنشاء دائرة أساسية
      const circle = turf.circle([centerLng, centerLat], radius, { steps: 120, units: 'kilometers' });
      const polygonElements: JSX.Element[] = [];
      
      // قطع الدائرة بالعوائق
      let remaining = circle;
      obstaclePolygons.forEach((obstacle, index) => {
        if (remaining && obstacle.geometry.type === 'Polygon') {
          try {
            // تحديد تأثير العائق على الرؤية
            const obstacleType = obstacle.properties.obstacleType;
            let blockageEffect = 1.0; // تأثير الحجب الكامل افتراضياً
            
            switch (obstacleType) {
              case 'urban_area': blockageEffect = 0.8; break; // المدن تحجب 80%
              case 'mountain': blockageEffect = 0.9; break; // الجبال تحجب 90%
              case 'forest': blockageEffect = 0.6; break; // الغابات تحجب 60%
              case 'river': blockageEffect = 0.3; break; // الأنهار تحجب 30%
              case 'airport': blockageEffect = 0.7; break; // المطارات تحجب 70%
              case 'military_base': blockageEffect = 0.9; break; // القواعد العسكرية تحجب 90%
              default: blockageEffect = 0.5; break;
            }
            
            const obstacleFeature = turf.feature(obstacle.geometry);
            const intersection = turf.intersect(remaining, obstacleFeature);
            
            if (intersection) {
              // إضافة منطقة متأثرة بالعوائق
              const affectedCoords = intersection.geometry.type === 'Polygon' 
                ? [intersection.geometry.coordinates] 
                : intersection.geometry.coordinates;
              
              affectedCoords.forEach((coords, coordIndex) => {
                polygonElements.push(
                  <Polygon
                    key={`${keyPrefix}-obstacle-affected-${unit.id}-${index}-${coordIndex}`}
                    positions={coords[0].map(coord => [coord[1], coord[0]])}
                    pathOptions={{
                      color: color,
                      fillColor: '#666666', // لون رمادي للمناطق المحجوبة
                      fillOpacity: 0.1 * blockageEffect,
                      weight: 2,
                      dashArray: '3 3',
                      opacity: 0.5
                    }}
                  />
                );
              });
              
              // إزالة المنطقة المحجوبة من الدائرة الأساسية
              const difference = turf.difference(remaining, obstacleFeature);
              if (difference) remaining = difference;
            }
          } catch (error) {
            console.warn('خطأ في معالجة العوائق:', error);
          }
        }
      });
      
      // رسم الجزء المتبقي من الدائرة (المناطق غير المحجوبة)
      if (remaining && remaining.geometry.type === 'Polygon') {
        const coords = remaining.geometry.coordinates;
        coords.forEach((ring, ringIndex) => {
          polygonElements.push(
            <Polygon
              key={`${keyPrefix}-clear-${unit.id}-${ringIndex}`}
              positions={ring.map(coord => [coord[1], coord[0]])}
              pathOptions={{
                color: color,
                fillColor: fillColor,
                fillOpacity: 0.25,
                weight: 3,
                opacity: 0.8
              }}
            />
          );
        });
      }
      
      return polygonElements;
    } catch (error) {
      console.warn('خطأ في رسم الدائرة مع العوائق:', error);
      // العودة إلى دائرة عادية في حالة الخطأ
      return [
        <Circle
          key={`${keyPrefix}-fallback-${unit.id}`}
          center={[centerLat, centerLng]}
          radius={radius * 1000}
          pathOptions={{ color, fillColor, fillOpacity: 0.2, weight: 3 }}
        />
      ];
    }
  };

  return [
    ...renderClippedByObstacles(blind, '#059669', '#10b981', 'blind-obstacles'),
    ...renderClippedByObstacles(kill, '#dc2626', '#ef4444', 'kill-obstacles'),
    ...renderClippedByObstacles(nonkill, '#d97706', '#f59e0b', 'nonkill-obstacles')
  ];
};
