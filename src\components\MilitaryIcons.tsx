import React from 'react';

// رموز عسكرية vector للتشكيلات
export const MilitaryUnitIcons = {
  // تشكيلات صديقة
  friendly: {
    infantry: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز المشاة */}
        <path d="M8 6h8v2H8V6zm0 4h8v2H8v-2zm0 4h8v2H8v-2z" fill="white"/>
        <circle cx="12" cy="16" r="1" fill="white"/>
      </svg>
    ),
    
    armor: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز المدرعات - دائرة مع خط */}
        <circle cx="12" cy="12" r="6" fill="none" stroke="white" strokeWidth="2"/>
        <line x1="8" y1="12" x2="16" y2="12" stroke="white" strokeWidth="2"/>
      </svg>
    ),
    
    artillery: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز المدفعية - دائرة مملوءة */}
        <circle cx="12" cy="12" r="4" fill="white"/>
        <circle cx="12" cy="12" r="2" fill={color}/>
      </svg>
    ),
    
    mechanized: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز الآليات - مربع مع دائرة */}
        <rect x="8" y="8" width="8" height="8" fill="none" stroke="white" strokeWidth="2"/>
        <circle cx="12" cy="12" r="2" fill="white"/>
      </svg>
    ),
    
    airDefense: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز الدفاع الجوي - مثلث مع خطوط */}
        <path d="M12 6 L18 16 L6 16 Z" fill="white"/>
        <line x1="12" y1="10" x2="12" y2="14" stroke={color} strokeWidth="1"/>
        <line x1="10" y1="12" x2="14" y2="12" stroke={color} strokeWidth="1"/>
      </svg>
    ),
    
    logistics: (size: number = 24, color: string = '#3b82f6') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* مربع أزرق للقوات الصديقة */}
        <rect x="2" y="2" width="20" height="20" fill={color} stroke="#1e40af" strokeWidth="2"/>
        {/* رمز اللوجستيات - صندوق */}
        <rect x="7" y="7" width="10" height="6" fill="none" stroke="white" strokeWidth="2"/>
        <line x1="9" y1="9" x2="15" y2="9" stroke="white" strokeWidth="1"/>
        <line x1="9" y1="11" x2="15" y2="11" stroke="white" strokeWidth="1"/>
      </svg>
    )
  },
  
  // تشكيلات عدو
  enemy: {
    infantry: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز المشاة */}
        <path d="M8 8h8v2H8V8zm0 3h8v2H8v-2zm0 3h8v2H8v-2z" fill="white"/>
      </svg>
    ),
    
    armor: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز المدرعات */}
        <circle cx="12" cy="12" r="5" fill="none" stroke="white" strokeWidth="2"/>
        <line x1="8" y1="12" x2="16" y2="12" stroke="white" strokeWidth="2"/>
      </svg>
    ),
    
    artillery: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز المدفعية */}
        <circle cx="12" cy="12" r="3" fill="white"/>
        <circle cx="12" cy="12" r="1.5" fill={color}/>
      </svg>
    ),
    
    mechanized: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز الآليات */}
        <rect x="9" y="9" width="6" height="6" fill="none" stroke="white" strokeWidth="2"/>
        <circle cx="12" cy="12" r="1.5" fill="white"/>
      </svg>
    ),
    
    airDefense: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز الدفاع الجوي */}
        <path d="M12 7 L17 15 L7 15 Z" fill="white"/>
        <line x1="12" y1="10" x2="12" y2="13" stroke={color} strokeWidth="1"/>
        <line x1="10.5" y1="11.5" x2="13.5" y2="11.5" stroke={color} strokeWidth="1"/>
      </svg>
    ),
    
    logistics: (size: number = 24, color: string = '#ef4444') => (
      <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
        {/* معين أحمر للقوات المعادية */}
        <path d="M12 2 L22 12 L12 22 L2 12 Z" fill={color} stroke="#dc2626" strokeWidth="2"/>
        {/* رمز اللوجستيات */}
        <rect x="8" y="8" width="8" height="5" fill="none" stroke="white" strokeWidth="2"/>
        <line x1="9" y1="9.5" x2="15" y2="9.5" stroke="white" strokeWidth="1"/>
        <line x1="9" y1="11" x2="15" y2="11" stroke="white" strokeWidth="1"/>
      </svg>
    )
  }
};

// رموز العتاد العسكري
export const MilitaryEquipmentIcons = {
  // دبابات
  tank: (size: number = 20, color: string = '#059669') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="4" y="8" width="16" height="8" rx="2" fill={color} stroke="#047857" strokeWidth="1"/>
      <rect x="6" y="10" width="12" height="4" fill="#047857"/>
      <circle cx="7" cy="16" r="2" fill="#047857"/>
      <circle cx="17" cy="16" r="2" fill="#047857"/>
      <line x1="18" y1="12" x2="22" y2="10" stroke={color} strokeWidth="2"/>
    </svg>
  ),
  
  // مدفعية
  artillery: (size: number = 20, color: string = '#dc2626') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="2" y="10" width="8" height="4" fill={color}/>
      <line x1="10" y1="12" x2="20" y2="8" stroke={color} strokeWidth="3"/>
      <circle cx="6" cy="16" r="2" fill="#991b1b"/>
      <circle cx="14" cy="16" r="2" fill="#991b1b"/>
    </svg>
  ),
  
  // صاروخ
  missile: (size: number = 20, color: string = '#7c3aed') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="8" y="4" width="8" height="16" rx="4" fill={color}/>
      <path d="M12 2 L16 6 L8 6 Z" fill="#5b21b6"/>
      <rect x="10" y="8" width="4" height="8" fill="#5b21b6"/>
      <path d="M6 14 L8 12 L8 16 Z" fill="#a855f7"/>
      <path d="M18 14 L16 12 L16 16 Z" fill="#a855f7"/>
    </svg>
  ),
  
  // طائرة
  aircraft: (size: number = 20, color: string = '#0891b2') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <path d="M12 2 L20 18 L12 16 L4 18 Z" fill={color}/>
      <rect x="11" y="6" width="2" height="8" fill="#0e7490"/>
      <path d="M6 10 L12 8 L18 10 L12 12 Z" fill="#0e7490"/>
    </svg>
  ),
  
  // مركبة مدرعة
  apc: (size: number = 20, color: string = '#059669') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="3" y="8" width="18" height="8" rx="3" fill={color}/>
      <rect x="5" y="10" width="14" height="4" fill="#047857"/>
      <circle cx="6" cy="17" r="1.5" fill="#047857"/>
      <circle cx="12" cy="17" r="1.5" fill="#047857"/>
      <circle cx="18" cy="17" r="1.5" fill="#047857"/>
      <rect x="8" y="6" width="8" height="2" fill={color}/>
    </svg>
  ),
  
  // رادار
  radar: (size: number = 20, color: string = '#7c2d12') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="8" fill="none" stroke={color} strokeWidth="2"/>
      <circle cx="12" cy="12" r="5" fill="none" stroke={color} strokeWidth="1"/>
      <circle cx="12" cy="12" r="2" fill="none" stroke={color} strokeWidth="1"/>
      <line x1="12" y1="12" x2="18" y2="8" stroke={color} strokeWidth="2"/>
      <circle cx="12" cy="12" r="1" fill={color}/>
    </svg>
  ),
  
  // شاحنة إمداد
  supply: (size: number = 20, color: string = '#65a30d') => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <rect x="2" y="8" width="12" height="8" fill={color}/>
      <rect x="14" y="10" width="6" height="6" fill="#4d7c0f"/>
      <circle cx="6" cy="17" r="2" fill="#365314"/>
      <circle cx="18" cy="17" r="2" fill="#365314"/>
      <rect x="4" y="10" width="8" height="4" fill="#4d7c0f"/>
    </svg>
  )
};

// دالة لتحديد الرمز المناسب للوحدة
export const getUnitIcon = (unit: any, size: number = 24) => {
  const unitType = unit.type?.toLowerCase() || 'infantry';
  const side = unit.side || 'friendly';
  
  if (side === 'friendly') {
    switch (unitType) {
      case 'armor':
      case 'tank':
        return MilitaryUnitIcons.friendly.armor(size);
      case 'artillery':
        return MilitaryUnitIcons.friendly.artillery(size);
      case 'mechanized':
        return MilitaryUnitIcons.friendly.mechanized(size);
      case 'air_defense':
      case 'airdefense':
        return MilitaryUnitIcons.friendly.airDefense(size);
      case 'logistics':
      case 'supply':
        return MilitaryUnitIcons.friendly.logistics(size);
      default:
        return MilitaryUnitIcons.friendly.infantry(size);
    }
  } else {
    switch (unitType) {
      case 'armor':
      case 'tank':
        return MilitaryUnitIcons.enemy.armor(size);
      case 'artillery':
        return MilitaryUnitIcons.enemy.artillery(size);
      case 'mechanized':
        return MilitaryUnitIcons.enemy.mechanized(size);
      case 'air_defense':
      case 'airdefense':
        return MilitaryUnitIcons.enemy.airDefense(size);
      case 'logistics':
      case 'supply':
        return MilitaryUnitIcons.enemy.logistics(size);
      default:
        return MilitaryUnitIcons.enemy.infantry(size);
    }
  }
};

// دالة لتحديد رمز العتاد
export const getEquipmentIcon = (equipment: any, size: number = 20) => {
  const equipType = equipment.type?.toLowerCase() || 'tank';
  
  switch (equipType) {
    case 'tank':
    case 'main_battle_tank':
      return MilitaryEquipmentIcons.tank(size);
    case 'artillery':
    case 'howitzer':
      return MilitaryEquipmentIcons.artillery(size);
    case 'missile':
    case 'sam':
      return MilitaryEquipmentIcons.missile(size);
    case 'aircraft':
    case 'helicopter':
      return MilitaryEquipmentIcons.aircraft(size);
    case 'apc':
    case 'ifv':
      return MilitaryEquipmentIcons.apc(size);
    case 'radar':
      return MilitaryEquipmentIcons.radar(size);
    case 'supply':
    case 'truck':
      return MilitaryEquipmentIcons.supply(size);
    default:
      return MilitaryEquipmentIcons.tank(size);
  }
};
