import React from 'react';
import { Polygon, Popup } from 'react-leaflet';
import * as turf from '@turf/turf';

// بيانات التضاريس المحسنة (جبال، تلال، وديان)
export const terrainFeatures = [
  // جبال القلمون (شمال دمشق)
  {
    type: 'Feature',
    properties: { name: 'جبال القلمون', elevation: 2800, terrainType: 'mountain' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.4, 33.8], [36.6, 33.9], [36.7, 33.7], [36.5, 33.6], [36.3, 33.7], [36.4, 33.8]
      ]]
    }
  },
  // جبال الساحل السوري
  {
    type: 'Feature',
    properties: { name: 'جبال الساحل', elevation: 1500, terrainType: 'mountain' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [35.8, 35.5], [36.0, 35.7], [36.1, 35.4], [35.9, 35.2], [35.7, 35.3], [35.8, 35.5]
      ]]
    }
  },
  // جبل الشيخ (جبل حرمون)
  {
    type: 'Feature',
    properties: { name: 'جبل الشيخ', elevation: 2814, terrainType: 'mountain' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [35.7, 33.3], [35.9, 33.4], [36.0, 33.2], [35.8, 33.1], [35.6, 33.2], [35.7, 33.3]
      ]]
    }
  },
  // تلال حوران
  {
    type: 'Feature',
    properties: { name: 'تلال حوران', elevation: 800, terrainType: 'hills' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.2, 32.8], [36.5, 33.0], [36.6, 32.7], [36.3, 32.5], [36.1, 32.6], [36.2, 32.8]
      ]]
    }
  },
  // وادي الفرات
  {
    type: 'Feature',
    properties: { name: 'وادي الفرات', elevation: 200, terrainType: 'valley' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [40.0, 35.0], [40.5, 35.2], [40.8, 34.8], [40.3, 34.5], [39.8, 34.7], [40.0, 35.0]
      ]]
    }
  },
  // تلال إدلب
  {
    type: 'Feature',
    properties: { name: 'تلال إدلب', elevation: 600, terrainType: 'hills' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.5, 35.8], [36.8, 36.0], [36.9, 35.7], [36.6, 35.5], [36.4, 35.6], [36.5, 35.8]
      ]]
    }
  }
];

// عوائق متنوعة (مدن، مطارات، قواعد عسكرية، أنهار)
export const obstaclePolygons = [
  // مطار دمشق الدولي
  {
    type: 'Feature',
    properties: { name: 'مطار دمشق الدولي', obstacleType: 'airport' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.51, 33.41], [36.53, 33.42], [36.52, 33.40], [36.50, 33.39], [36.51, 33.41]
      ]]
    }
  },
  // مطار حلب الدولي
  {
    type: 'Feature',
    properties: { name: 'مطار حلب الدولي', obstacleType: 'airport' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [37.22, 36.18], [37.25, 36.19], [37.24, 36.16], [37.21, 36.15], [37.22, 36.18]
      ]]
    }
  },
  // قاعدة حميميم الجوية
  {
    type: 'Feature',
    properties: { name: 'قاعدة حميميم الجوية', obstacleType: 'military_base' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [35.95, 35.40], [35.98, 35.42], [35.97, 35.38], [35.94, 35.37], [35.95, 35.40]
      ]]
    }
  },
  // مدينة حلب (منطقة حضرية كثيفة)
  {
    type: 'Feature',
    properties: { name: 'مدينة حلب', obstacleType: 'urban_area' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [37.10, 36.15], [37.25, 36.25], [37.30, 36.10], [37.15, 36.05], [37.05, 36.10], [37.10, 36.15]
      ]]
    }
  },
  // مدينة دمشق (منطقة حضرية كثيفة)
  {
    type: 'Feature',
    properties: { name: 'مدينة دمشق', obstacleType: 'urban_area' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.25, 33.48], [36.35, 33.55], [36.40, 33.45], [36.30, 33.40], [36.20, 33.45], [36.25, 33.48]
      ]]
    }
  },
  // نهر الفرات (عائق مائي)
  {
    type: 'Feature',
    properties: { name: 'نهر الفرات', obstacleType: 'river' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [40.0, 35.5], [40.8, 35.8], [41.0, 35.6], [40.2, 35.3], [40.0, 35.5]
      ]]
    }
  },
  // نهر العاصي
  {
    type: 'Feature',
    properties: { name: 'نهر العاصي', obstacleType: 'river' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.7, 35.1], [36.9, 35.3], [37.0, 35.0], [36.8, 34.8], [36.6, 35.0], [36.7, 35.1]
      ]]
    }
  },
  // منطقة صناعية في حمص
  {
    type: 'Feature',
    properties: { name: 'المنطقة الصناعية - حمص', obstacleType: 'industrial' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.70, 34.70], [36.75, 34.72], [36.74, 34.68], [36.69, 34.67], [36.70, 34.70]
      ]]
    }
  },
  // مصفاة بانياس
  {
    type: 'Feature',
    properties: { name: 'مصفاة بانياس', obstacleType: 'refinery' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [35.94, 35.18], [35.97, 35.20], [35.96, 35.16], [35.93, 35.15], [35.94, 35.18]
      ]]
    }
  },
  // سد الفرات
  {
    type: 'Feature',
    properties: { name: 'سد الفرات', obstacleType: 'dam' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [38.05, 35.85], [38.08, 35.87], [38.07, 35.83], [38.04, 35.82], [38.05, 35.85]
      ]]
    }
  },
  // منطقة أثرية - تدمر
  {
    type: 'Feature',
    properties: { name: 'مدينة تدمر الأثرية', obstacleType: 'archaeological' },
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [38.25, 34.55], [38.30, 34.57], [38.29, 34.53], [38.24, 34.52], [38.25, 34.55]
      ]]
    }
  }
];

// دالة إنشاء مجموعة التضاريس
export const createTerrainCollection = () => {
  return turf.featureCollection(terrainFeatures);
};

// دالة إنشاء مجموعة العوائق
export const createObstaclesCollection = () => {
  return turf.featureCollection(obstaclePolygons);
};

// مكون رسم طبقة التضاريس
interface TerrainLayerProps {
  showTerrain: boolean;
}

export const TerrainLayer: React.FC<TerrainLayerProps> = ({ showTerrain }) => {
  if (!showTerrain) return null;
  
  return (
    <>
      {terrainFeatures.map((terrain, index) => {
        if (terrain.geometry.type === 'Polygon') {
          const coords = terrain.geometry.coordinates[0];
          const terrainType = terrain.properties.terrainType;
          const elevation = terrain.properties.elevation;
          
          // تحديد اللون حسب نوع التضاريس والارتفاع
          let color = '#10b981';
          let fillColor = '#34d399';
          
          switch (terrainType) {
            case 'mountain': 
              color = elevation > 2000 ? '#7c2d12' : '#a3a3a3';
              fillColor = elevation > 2000 ? '#fed7aa' : '#d4d4d8';
              break;
            case 'hills': 
              color = '#65a30d'; 
              fillColor = '#a3e635'; 
              break;
            case 'valley': 
              color = '#059669'; 
              fillColor = '#34d399'; 
              break;
            default: 
              color = '#10b981'; 
              fillColor = '#34d399'; 
              break;
          }
          
          return (
            <Polygon
              key={`terrain-${index}`}
              positions={coords.map(coord => [coord[1], coord[0]])}
              pathOptions={{
                color: color,
                fillColor: fillColor,
                fillOpacity: 0.2,
                weight: 2,
                opacity: 0.6
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-bold">{terrain.properties.name}</h3>
                  <p>النوع: {terrainType}</p>
                  <p>الارتفاع: {elevation} متر</p>
                </div>
              </Popup>
            </Polygon>
          );
        }
        return null;
      })}
    </>
  );
};

// مكون رسم طبقة العوائق
interface ObstacleLayerProps {
  showObstacles: boolean;
}

export const ObstacleLayer: React.FC<ObstacleLayerProps> = ({ showObstacles }) => {
  if (!showObstacles) return null;
  
  return (
    <>
      {obstaclePolygons.map((obstacle, index) => {
        if (obstacle.geometry.type === 'Polygon') {
          const coords = obstacle.geometry.coordinates[0];
          const obstacleType = obstacle.properties.obstacleType;
          
          // تحديد اللون حسب نوع العائق
          let color = '#666666';
          let fillColor = '#999999';
          
          switch (obstacleType) {
            case 'urban_area': color = '#8b5cf6'; fillColor = '#c4b5fd'; break;
            case 'airport': color = '#06b6d4'; fillColor = '#67e8f9'; break;
            case 'military_base': color = '#dc2626'; fillColor = '#fca5a5'; break;
            case 'river': color = '#0ea5e9'; fillColor = '#7dd3fc'; break;
            case 'industrial': color = '#ea580c'; fillColor = '#fdba74'; break;
            case 'refinery': color = '#7c2d12'; fillColor = '#fed7aa'; break;
            case 'dam': color = '#1e40af'; fillColor = '#93c5fd'; break;
            case 'archaeological': color = '#a3a3a3'; fillColor = '#d4d4d8'; break;
            default: color = '#666666'; fillColor = '#999999'; break;
          }
          
          return (
            <Polygon
              key={`obstacle-${index}`}
              positions={coords.map(coord => [coord[1], coord[0]])}
              pathOptions={{
                color: color,
                fillColor: fillColor,
                fillOpacity: 0.3,
                weight: 2,
                opacity: 0.7
              }}
            >
              <Popup>
                <div className="p-2">
                  <h3 className="font-bold">{obstacle.properties.name}</h3>
                  <p>النوع: {obstacleType}</p>
                </div>
              </Popup>
            </Polygon>
          );
        }
        return null;
      })}
    </>
  );
};
