import  { Equipment, Unit, Analysis, EquipmentType, AITargetingResult } from '../types';

// Generate a unique ID with a prefix
export function generateId(prefix: string): string {
  return `${prefix}_${Math.random().toString(36).substr(2, 9)}`;
}

// Syria's governorates coordinates (approximate) - تصحيح المواقع داخل حدود سوريا
const syriaCoordinates = {
  damascus: [33.5138, 36.2765],        // دمشق
  aleppo: [36.2021, 37.1343],          // حلب
  homs: [34.7324, 36.7137],            // حمص
  hama: [35.1355, 36.7572],            // حماة
  latakia: [35.5317, 35.7905],         // اللاذقية - تصحيح الموقع
  deir_ez_zor: [35.3359, 40.1408],     // دير الزور
  idlib: [35.9306, 36.6339],           // إدلب
  hasaka: [36.5024, 40.7563],          // الحسكة
  raqqa: [35.9528, 39.0088],           // الرقة
  daraa: [32.6256, 36.1055],           // درعا
  tartus: [34.8897, 35.8866],          // طرطوس - تصحيح الموقع
  // مواقع إضافية داخل سوريا
  sweida: [32.7094, 36.5681],          // السويداء
  quneitra: [33.1264, 35.8244],        // القنيطرة
  palmyra: [34.5581, 38.2669]          // تدمر
};

// Helper function to create a polygon around a center point
function createPolygon(center: number[], radius: number, sides: number = 6): number[][][] {
  const vertices = [];
  for (let i = 0; i < sides; i++) {
    const angle = (Math.PI * 2 * i) / sides;
    vertices.push([
      center[0] + radius * Math.cos(angle),
      center[1] + radius * Math.sin(angle)
    ]);
  }
  // Close the polygon
  vertices.push([...vertices[0]]);
  return [vertices];
}

// Create a smaller polygon for child units with more dispersion
function createChildPolygon(parentPolygon: number[][][], index: number, totalChildren: number): number[][][] {
  const parentVertices = parentPolygon[0];
  const parentCenter = parentVertices.reduce(
    (acc, vertex) => [acc[0] + vertex[0] / parentVertices.length, acc[1] + vertex[1] / parentVertices.length],
    [0, 0]
  );
  
  // زيادة نصف القطر للوحدات الفرعية لتكون أكثر تباعداً
  const childRadius = 0.015 + (index * 0.003); // حجم أصغر للوحدات الفرعية
  
  // زيادة معامل التباعد لتوزيع الوحدات الفرعية على مساحة أكبر
  const dispersionFactor = 3.0; // معامل أكبر يعني تباعد أكبر
  
  // إضافة عنصر عشوائي للزاوية لتجنب الترتيب المنتظم
  const randomAngleOffset = (Math.random() - 0.5) * 0.5;
  const angle = (Math.PI * 2 * index) / totalChildren + randomAngleOffset;
  
  // حساب مركز الوحدة الفرعية مع زيادة التباعد
  const childCenter = [
    parentCenter[0] + childRadius * dispersionFactor * Math.cos(angle),
    parentCenter[1] + childRadius * dispersionFactor * Math.sin(angle)
  ];
  
  return createPolygon(childCenter, childRadius);
}

// Helper: إرجاع نقطة عشوائية ضمن حدود سوريا مع تحسين التوزيع
function randomSyriaPoint() {
  // تقسيم سوريا إلى مناطق (شمال، جنوب، شرق، غرب، وسط)
  const regions = [
    // الشمال الغربي (حلب، إدلب)
    { minLat: 35.5, maxLat: 37.5, minLng: 35.5, maxLng: 38.0 },
    // الشمال الشرقي (الحسكة، الرقة)
    { minLat: 35.5, maxLat: 37.5, minLng: 38.0, maxLng: 42.0 },
    // الوسط (حمص، حماة)
    { minLat: 34.0, maxLat: 35.5, minLng: 36.0, maxLng: 39.0 },
    // الجنوب الغربي (دمشق، درعا)
    { minLat: 32.0, maxLat: 34.0, minLng: 35.5, maxLng: 37.5 },
    // الجنوب الشرقي (تدمر، دير الزور)
    { minLat: 32.0, maxLat: 35.5, minLng: 37.5, maxLng: 42.0 },
    // الساحل (اللاذقية، طرطوس)
    { minLat: 34.0, maxLat: 36.0, minLng: 35.5, maxLng: 36.0 }
  ];
  
  // اختيار منطقة عشوائية
  const region = regions[Math.floor(Math.random() * regions.length)];
  
  // إنشاء نقطة عشوائية ضمن المنطقة المختارة
  const lat = region.minLat + Math.random() * (region.maxLat - region.minLat);
  const lng = region.minLng + Math.random() * (region.maxLng - region.minLng);
  
  return [lat, lng];
}

// قائمة المدن والمناطق السورية مع الإحداثيات (موسعة لتغطية مساحة أكبر)
const syrianLocations = [
  // المناطق الغربية (الساحل والمناطق المجاورة)
  { name: 'اللاذقية', coords: [35.5317, 35.7905], side: 'friendly' },
  { name: 'طرطوس', coords: [34.8897, 35.8866], side: 'friendly' },
  { name: 'بانياس', coords: [35.1833, 35.9500], side: 'friendly' },
  { name: 'جبلة', coords: [35.3667, 35.9333], side: 'friendly' },
  { name: 'القرداحة', coords: [35.4167, 36.0667], side: 'friendly' },
  
  // المناطق الوسطى
  { name: 'حمص', coords: [34.7324, 36.7137], side: 'friendly' },
  { name: 'حماة', coords: [35.1355, 36.7572], side: 'friendly' },
  { name: 'السلمية', coords: [35.0167, 37.0500], side: 'friendly' },
  { name: 'مصياف', coords: [35.0667, 36.3333], side: 'friendly' },
  
  // المناطق الجنوبية
  { name: 'دمشق', coords: [33.5138, 36.2765], side: 'friendly' },
  { name: 'ريف دمشق', coords: [33.4500, 36.5000], side: 'friendly' },
  { name: 'القنيطرة', coords: [33.1167, 35.8333], side: 'friendly' },
  
  // المناطق الشمالية
  { name: 'حلب', coords: [36.2021, 37.1343], side: 'enemy' },
  { name: 'إدلب', coords: [35.9306, 36.6339], side: 'enemy' },
  { name: 'عفرين', coords: [36.5119, 36.8653], side: 'enemy' },
  { name: 'منبج', coords: [36.5281, 37.9549], side: 'enemy' },
  { name: 'الباب', coords: [36.3705, 37.5176], side: 'enemy' },
  
  // المناطق الشرقية
  { name: 'الرقة', coords: [35.9528, 39.0088], side: 'enemy' },
  { name: 'دير الزور', coords: [35.3359, 40.1408], side: 'enemy' },
  { name: 'الحسكة', coords: [36.5024, 40.7563], side: 'enemy' },
  { name: 'القامشلي', coords: [37.0500, 41.2167], side: 'enemy' },
  
  // المناطق الجنوبية الشرقية
  { name: 'درعا', coords: [32.6256, 36.1055], side: 'enemy' },
  { name: 'السويداء', coords: [32.7044, 36.5666], side: 'enemy' },
  { name: 'تدمر', coords: [34.5521, 38.2841], side: 'enemy' }
];

// Helper: انحراف عشوائي أكبر حول المدينة لزيادة التشتت
function jitterCoords(center: number[], maxJitter = 0.15) {
  // زيادة الانحراف العشوائي بشكل كبير
  const jitterFactor = maxJitter * 2.5; // زيادة معامل الانحراف
  
  // استخدام توزيع غير متساوٍ للحصول على تشتت أكثر طبيعية
  // في بعض الأحيان، نضيف انحرافاً كبيراً جداً
  let latJitter, lngJitter;
  
  if (Math.random() > 0.7) {
    // في 30% من الحالات، استخدم انحرافاً كبيراً جداً
    latJitter = (Math.random() - 0.5) * jitterFactor * 2;
    lngJitter = (Math.random() - 0.5) * jitterFactor * 2;
  } else {
    // في 70% من الحالات، استخدم انحرافاً معتدلاً
    latJitter = (Math.random() - 0.5) * jitterFactor;
    lngJitter = (Math.random() - 0.5) * jitterFactor;
  }
  
  const lat = center[0] + latJitter;
  const lng = center[1] + lngJitter;
  
  return [lat, lng];
}

// توزيع الوحدات الصديقة والمعادية على مناطق مختلفة من سوريا
const friendlyLocations = syrianLocations.filter(loc => loc.side === 'friendly');
const enemyLocations = syrianLocations.filter(loc => loc.side === 'enemy');

// إنشاء 6 وحدات صديقة موزعة داخل حدود سوريا بعيداً عن البحر
const friendlyDivisions = [
  {
    name: 'الفرقة المدرعة الأولى',
    type: 'armor',
    location: createPolygon(jitterCoords(syriaCoordinates.damascus, 0.2), 0.04), // دمشق
    personnelCount: 2800
  },
  {
    name: 'فرقة المشاة الثانية',
    type: 'infantry',
    location: createPolygon(jitterCoords(syriaCoordinates.homs, 0.2), 0.035), // حمص
    personnelCount: 3200
  },
  {
    name: 'لواء المدفعية',
    type: 'artillery',
    location: createPolygon(jitterCoords(syriaCoordinates.hama, 0.2), 0.03), // حماة
    personnelCount: 1500
  },
  {
    name: 'الفرقة الآلية الساحلية',
    type: 'mechanized',
    location: createPolygon(jitterCoords([35.4, 35.9], 0.2), 0.04), // داخل الساحل
    personnelCount: 2400
  },
  {
    name: 'لواء الدفاع الجوي',
    type: 'air_defense',
    location: createPolygon(jitterCoords(syriaCoordinates.sweida, 0.2), 0.025), // السويداء
    personnelCount: 1200
  },
  {
    name: 'الحرس الجمهوري',
    type: 'armor',
    location: createPolygon(jitterCoords(syriaCoordinates.quneitra, 0.2), 0.035), // القنيطرة
    personnelCount: 2600
  }
];

// إنشاء 4 وحدات معادية موزعة بعيداً عن حدود فلسطين (في الشمال والشرق)
const enemyDivisions = [
  {
    name: 'فرقة المتمردين المدرعة',
    type: 'armor',
    location: createPolygon(jitterCoords(syriaCoordinates.idlib, 0.3), 0.04), // إدلب - شمال غرب
    personnelCount: 2200
  },
  {
    name: 'كتائب المشاة المعادية',
    type: 'infantry',
    location: createPolygon(jitterCoords(syriaCoordinates.hasaka, 0.3), 0.035), // الحسكة - شمال شرق
    personnelCount: 2800
  },
  {
    name: 'مجموعة المدفعية',
    type: 'artillery',
    location: createPolygon(jitterCoords(syriaCoordinates.deir_ez_zor, 0.3), 0.03), // دير الزور - شرق
    personnelCount: 1300
  },
  {
    name: 'الوحدة الآلية المعادية',
    type: 'mechanized',
    location: createPolygon(jitterCoords(syriaCoordinates.raqqa, 0.3), 0.04), // الرقة - شمال شرق
    personnelCount: 2000
  },
  {
    name: 'قوات المعارضة الشمالية',
    type: 'infantry',
    location: createPolygon(jitterCoords(syriaCoordinates.aleppo, 0.4), 0.035), // حلب - شمال
    personnelCount: 2500
  }
];

// Initialize units (changed to a function to return initial data)
export function getInitialUnits(): Unit[] {
  const initialUnits: Unit[] = [];
  const divisionIds: Record<string, string> = {};

  // Add friendly divisions
  friendlyDivisions.forEach(div => {
    const id = generateId('unit');
    divisionIds[div.name] = id;

    const newUnit: Unit = {
      id,
      name: div.name,
      type: div.type || 'infantry',
      side: 'friendly',
      commander: `قائد ${div.name}`,
      personnelCount: div.personnelCount,
      parentId: null,
      children: [],
      location: {
        type: 'polygon',
        coordinates: div.location
      },
      status: 'active',
      equipmentIds: [],
      readiness: 85 + Math.floor(Math.random() * 15)
    };
    initialUnits.push(newUnit);
  });

  // Add enemy divisions
  enemyDivisions.forEach(div => {
    const id = generateId('unit');
    divisionIds[div.name] = id;

    const newUnit: Unit = {
      id,
      name: div.name,
      type: div.type || 'infantry',
      side: 'enemy',
      commander: `قائد ${div.name}`,
      personnelCount: div.personnelCount,
      parentId: null,
      children: [],
      location: {
        type: 'polygon',
        coordinates: div.location
      },
      status: 'active',
      equipmentIds: [],
      readiness: 75 + Math.floor(Math.random() * 20)
    };
    initialUnits.push(newUnit);
  });

  // Create brigades for each division with more dispersion
  Object.entries(divisionIds).forEach(([divName, divId]) => {
    const division = initialUnits.find(u => u.id === divId);
    if (!division) return;

    const isFriendly = division.side === 'friendly';
    // زيادة عدد الألوية لتوزيع القوات بشكل أكبر
    const brigadeCount = isFriendly ? 4 : 3;

    for (let i = 0; i < brigadeCount; i++) {
      const brigadeId = generateId('unit');
      const brigadeName = `${isFriendly ? 'لواء' : 'كتيبة'} ${i + 1} - ${divName}`;
      const brigadePersonnel = Math.floor(division.personnelCount / brigadeCount);

      // إنشاء موقع اللواء مع تباعد أكبر
      const brigadeLocation = createChildPolygon(division.location.coordinates as number[][][], i, brigadeCount);

      const newBrigade: Unit = {
        id: brigadeId,
        name: brigadeName,
        type: 'brigade',
        side: division.side,
        commander: `قائد ${brigadeName}`,
        personnelCount: brigadePersonnel,
        parentId: divId,
        children: [],
        location: {
          type: 'polygon',
          coordinates: brigadeLocation
        },
        status: 'active',
        equipmentIds: [],
        readiness: 85 + Math.floor(Math.random() * 10)
      };
      initialUnits.push(newBrigade);

      // Add brigade to division's children
      division.children.push(newBrigade);

      // زيادة عدد الكتائب وتوزيعها بشكل أكبر
      const battalionCount = isFriendly ? 3 : 2;

      for (let j = 0; j < battalionCount; j++) {
        const battalionId = generateId('unit');
        const battalionName = `كتيبة ${j + 1} - ${brigadeName}`;
        const battalionPersonnel = Math.floor(brigadePersonnel / battalionCount);

        // إنشاء موقع الكتيبة مع تباعد أكبر
        // استخدام نقطة عشوائية في سوريا في بعض الحالات لزيادة التشتت
        let battalionLocation;
        if (Math.random() > 0.6) {
          // في 40% من الحالات، ضع الكتيبة في موقع عشوائي تماماً
          const randomPoint = randomSyriaPoint();
          battalionLocation = createPolygon(randomPoint, 0.015);
        } else {
          // في 60% من الحالات، ضع الكتيبة بالقرب من اللواء ولكن مع تباعد
          battalionLocation = createChildPolygon(brigadeLocation, j, battalionCount);
        }

        const newBattalion: Unit = {
          id: battalionId,
          name: battalionName,
          type: 'battalion',
          side: division.side,
          commander: `قائد ${battalionName}`,
          personnelCount: battalionPersonnel,
          parentId: brigadeId,
          children: [],
          location: {
            type: 'polygon',
            coordinates: battalionLocation
          },
          status: 'active',
          equipmentIds: [],
          readiness: 80 + Math.floor(Math.random() * 15)
        };
        initialUnits.push(newBattalion);

        // Add battalion to brigade's children
        const brigade = initialUnits.find(u => u.id === brigadeId);
        if (brigade) {
          brigade.children.push(newBattalion);
        }

        // تقليل عدد السرايا وتوزيعها بشكل أكبر
        // إنشاء سرايا فقط لبعض الكتائب (30% من الكتائب)
        if (Math.random() > 0.7) {
          const companyCount = 2; // تقليل عدد السرايا

          for (let k = 0; k < companyCount; k++) {
            const companyId = generateId('unit');
            const companyName = `سرية ${k + 1} - ${battalionName}`;
            const companyPersonnel = Math.floor(battalionPersonnel / companyCount);

            // إنشاء موقع السرية مع تباعد أكبر
            // في بعض الحالات، ضع السرية في موقع عشوائي تماماً
            let companyLocation;
            if (Math.random() > 0.5) {
              const randomPoint = randomSyriaPoint();
              companyLocation = createPolygon(randomPoint, 0.01);
            } else {
              companyLocation = createChildPolygon(battalionLocation, k, companyCount);
            }

            const newCompany: Unit = {
              id: companyId,
              name: companyName,
              type: 'company',
              side: division.side,
              commander: `قائد ${companyName}`,
              personnelCount: companyPersonnel,
              parentId: battalionId,
              children: [],
              location: {
                type: 'polygon',
                coordinates: companyLocation
              },
              status: 'active',
              equipmentIds: [],
              readiness: 75 + Math.floor(Math.random() * 20)
            };
            initialUnits.push(newCompany);

            // Add company to battalion's children
            const battalion = initialUnits.find(u => u.id === battalionId);
            if (battalion) {
              battalion.children.push(newCompany);
            }
          }
        }
      }
    }
  });

  return initialUnits;
}

// Equipment templates by type
const equipmentTemplates: Record<EquipmentType, Omit<Equipment, 'id' | 'unitId'>> = {
  tank: {
    name: "دبابة T-72",
    type: "tank",
    status: "operational",
    quantity: 1,
    capabilities: ["attack", "defense"],
    range: 5,
    accuracy: 85,
    firepower: 8
  },
  apc: {
    name: "ناقلة جند BMP-1",
    type: "apc",
    status: "operational",
    quantity: 1,
    capabilities: ["transport", "defense"],
    range: 1.5,
    accuracy: 70,
    firepower: 4
  },
  artillery: {
    name: "مدفعية ذاتية الحركة 2S1",
    type: "artillery",
    status: "operational",
    quantity: 1,
    capabilities: ["attack"],
    range: 20,
    accuracy: 75,
    firepower: 7
  },
  anti_air: {
    name: "نظام دفاع جوي S-300",
    type: "anti_air",
    status: "operational",
    quantity: 1,
    capabilities: ["defense"],
    range: 150,
    accuracy: 90,
    firepower: 6
  },
  helicopter: {
    name: "مروحية Mi-24",
    type: "helicopter",
    status: "operational",
    quantity: 1,
    capabilities: ["attack", "transport", "reconnaissance"],
    range: 200,
    accuracy: 80,
    firepower: 9
  },
  fighter: {
    name: "طائرة مقاتلة MiG-29",
    type: "fighter",
    status: "operational",
    quantity: 1,
    capabilities: ["attack", "defense"],
    range: 500,
    accuracy: 92,
    firepower: 10
  },
  drone: {
    name: "طائرة بدون طيار RQ-11",
    type: "drone",
    status: "operational",
    quantity: 1,
    capabilities: ["reconnaissance"],
    range: 50,
    accuracy: 70,
    firepower: 2
  },
  missile: {
    name: "نظام صاروخي Scud",
    type: "missile",
    status: "operational",
    quantity: 1,
    capabilities: ["attack"],
    range: 300,
    accuracy: 60,
    firepower: 9
  },
  radar: {
    name: "رادار مراقبة P-18",
    type: "radar",
    status: "operational",
    quantity: 1,
    capabilities: ["reconnaissance"],
    range: 250,
    accuracy: 88,
    firepower: 1
  },
  logistics: {
    name: "شاحنة إمداد Kamaz",
    type: "logistics",
    status: "operational",
    quantity: 1,
    capabilities: ["transport"],
    range: 0,
    accuracy: 0,
    firepower: 0
  },
  medical: {
    name: "عربة إسعاف UAZ",
    type: "medical",
    status: "operational",
    quantity: 1,
    capabilities: ["medical"],
    range: 0,
    accuracy: 0,
    firepower: 0
  },
  command: {
    name: "مركبة قيادة BTR-80K",
    type: "command",
    status: "operational",
    quantity: 1,
    capabilities: ["communication"],
    range: 0,
    accuracy: 0,
    firepower: 0
  }
};

// Initialize equipment (changed to a function to return initial data)
export function getInitialEquipment(units: Unit[]): Equipment[] {
  const initialEquipment: Equipment[] = [];
  // Helper function to assign equipment to a unit
  const assignEquipmentToUnit = (unitId: string, equipmentId: string, currentUnits: Unit[]) => {
    const unit = currentUnits.find(u => u.id === unitId);
    if (unit) {
      unit.equipmentIds.push(equipmentId);
    }
  };

  units.forEach(unit => {
    // Create equipment based on unit type
    let equipmentTypes: EquipmentType[] = [];

    if (unit.side === 'friendly') {
      switch (unit.type) {
        case 'armor':
          equipmentTypes = ['tank', 'apc'];
          break;
        case 'artillery':
          equipmentTypes = ['artillery'];
          break;
        case 'mechanized':
          equipmentTypes = ['apc', 'tank'];
          break;
        case 'air_defense':
          equipmentTypes = ['anti_air', 'missile'];
          break;
        case 'infantry':
          equipmentTypes = ['apc'];
          break;
        default:
          equipmentTypes = ['tank'];
      }
    } else {
      switch (unit.type) {
        case 'armor':
          equipmentTypes = ['tank'];
          break;
        case 'artillery':
          equipmentTypes = ['artillery'];
          break;
        case 'mechanized':
          equipmentTypes = ['apc'];
          break;
        case 'infantry':
          equipmentTypes = ['apc'];
          break;
        default:
          equipmentTypes = ['tank'];
      }
    }

    // Create equipment for this unit
    equipmentTypes.forEach(eqType => {
      const template = equipmentTemplates[eqType];
      const quantity = Math.floor(Math.random() * 2) + 1; // تقليل الكمية

      const newEquipment: Equipment = {
        id: generateId('eq'),
        ...template,
        quantity,
        unitId: unit.id,
        status: Math.random() > 0.8 ? 'maintenance' : 'operational'
      };

      initialEquipment.push(newEquipment);
      assignEquipmentToUnit(unit.id, newEquipment.id, units); // Pass units here
    });
  });
  return initialEquipment;
}

// Initialize analyses (updated to use initialUnits and getInitialEquipment)
export function getInitialAnalyses(units: Unit[]): Analysis[] {
  return [
    {
      id: generateId('a'),
      title: "تحليل الموقف الدفاعي للفرقة الأولى",
      description: "تحليل لقدرات الدفاع ونقاط الضعف في خط الفرقة الأولى مقابل هجمات العدو المحتملة.",
      recommendation: "تعزيز الجناح الشمالي بوحدات مدرعة إضافية والتركيز على تحسين القدرات الاستخباراتية.",
      relatedUnitIds: [units.find(u => u.name === "الفرقة الصديقة 1")?.id || ''],
      timestamp: new Date().toISOString(),
      aiConfidence: 85,
      threatLevel: 'medium',
      type: 'defensive'
    },
    {
      id: generateId('a'),
      title: "فرص هجومية ضد وحدة المعارضة 202",
      description: "تحديد نقاط ضعف في انتشار وحدة المعارضة 202 يمكن استغلالها في عملية هجومية محدودة.",
      recommendation: "التخطيط لعملية التفاف من الجناح الشرقي مع استخدام المدفعية لإشغال الجبهة الرئيسية.",
      relatedUnitIds: [units.find(u => u.name === "وحدة العدو 2")?.id || ''],
      timestamp: new Date().toISOString(),
      aiConfidence: 72,
      threatLevel: 'medium',
      type: 'offensive'
    },
    {
      id: generateId('a'),
      title: "تحليل استخباراتي للتحركات في محافظة الحسكة",
      description: "رصد تحركات مشبوهة لقوافل إمداد تابعة للعدو في محافظة الحسكة تشير إلى احتمالية تعزيز مواقعهم.",
      recommendation: "زيادة المراقبة الجوية والاستطلاع في المنطقة وتجهيز قوات للتدخل السريع إذا لزم الأمر.",
      relatedUnitIds: [units.find(u => u.name === "وحدة العدو 3")?.id || ''],
      timestamp: new Date().toISOString(),
      aiConfidence: 68,
      threatLevel: 'low',
      type: 'intelligence'
    },
    {
      id: generateId('a'),
      title: "تقييم الموقف اللوجستي للفرقة السابعة",
      description: "تحليل لحالة الإمدادات والدعم اللوجستي للفرقة السابعة ومدى تأثيرها على الجاهزية القتالية.",
      recommendation: "تعزيز خطوط الإمداد وإنشاء مخزون احتياطي من الذخيرة والوقود في المواقع المتقدمة.",
      relatedUnitIds: [units.find(u => u.name === "الفرقة الصديقة 4")?.id || ''],
      timestamp: new Date().toISOString(),
      aiConfidence: 90,
      threatLevel: 'medium',
      type: 'logistical'
    }
  ];
}

// AI targeting results mock function
export function getAITargetRecommendations(targetUnitId: string, currentUnits: Unit[], currentEquipment: Equipment[]): AITargetingResult {
  const targetUnit = currentUnits.find(u => u.id === targetUnitId);
  if (!targetUnit || targetUnit.side === 'friendly') {
    throw new Error("وحدة غير صالحة للاستهداف");
  }
  
  // Find closest friendly units from currentUnits
  const friendlyUnits = currentUnits.filter(u => 
    u.side === 'friendly' && (u.type === 'battalion' || u.type === 'brigade')
  );
  
  // Calculate distances (simplified)
  const targetCoords = (targetUnit.location.coordinates as number[][][])[0][0];
  const unitsWithDistance = friendlyUnits.map(unit => {
    const unitCoords = (unit.location.coordinates as number[][][])[0][0];
    const distance = Math.sqrt(
      Math.pow(targetCoords[0] - unitCoords[0], 2) + 
      Math.pow(targetCoords[1] - unitCoords[1], 2)
    );
    return { unit, distance };
  });
  
  // Sort by distance
  unitsWithDistance.sort((a, b) => a.distance - b.distance);
  
  // Get closest unit
  const closestUnit = unitsWithDistance[0]?.unit;
  if (!closestUnit) {
    throw new Error("لا توجد وحدات صديقة متاحة للاستهداف");
  }
  
  // Find suitable equipment from currentEquipment
  const availableEquipment = currentEquipment.filter(eq => 
    eq.unitId === closestUnit.id && 
    eq.status === 'operational' && 
    ['artillery', 'tank', 'missile', 'helicopter', 'fighter'].includes(eq.type)
  );
  
  // Sort equipment by firepower
  availableEquipment.sort((a, b) => (b.firepower || 0) - (a.firepower || 0));
  
  // Select best equipment
  const recommendedEquipment = availableEquipment.slice(0, 2);
  
  // Generate success probability based on distance and equipment
  const maxRange = Math.max(...recommendedEquipment.map(eq => eq.range || 0));
  const normalizedDistance = unitsWithDistance[0].distance * 100; // Scale up for calculation
  
  let successProbability = 90 - (normalizedDistance / maxRange) * 30;
  successProbability = Math.min(95, Math.max(40, successProbability));
  
  // Generate recommendations
  const recommendations = [
    `استخدام ${recommendedEquipment[0]?.name || 'السلاح المتاح'} للهجوم الأولي`,
    `تأمين مسارات الانسحاب بعد الهجوم`,
    `مراقبة ردة فعل العدو للتدخل السريع إذا لزم الأمر`
  ];
  
  if (recommendedEquipment.length > 1) {
    recommendations.push(`استخدام ${recommendedEquipment[1]?.name || 'السلاح الثانوي'} كدعم للهجوم الرئيسي`);
  }
  
  if (normalizedDistance > maxRange / 2) {
    recommendations.push(`النظر في تحريك ${closestUnit.name} لمسافة أقرب للهدف لزيادة فعالية الهجوم`);
  }
  
  return {
    targetUnitId,
    targetType: 'unit',
    bestAttackerUnitId: closestUnit.id,
    recommendedEquipmentIds: recommendedEquipment.map(eq => eq.id),
    successProbability: Math.round(successProbability),
    collateralDamageRisk: 'low',
    recommendations
  };
}

// Helper function to calculate stats
export function calculateStats(units: Unit[], equipment: Equipment[]) {
  const friendlyUnits = units.filter(u => u.side === 'friendly');
  const enemyUnits = units.filter(u => u.side === 'enemy');
  
  const friendlyEquipment = equipment.filter(e => {
    const unit = units.find(u => u.id === e.unitId);
    return unit && unit.side === 'friendly';
  });
  
  const enemyEquipment = equipment.filter(e => {
    const unit = units.find(u => u.id === e.unitId);
    return unit && unit.side === 'enemy';
  });
  
  // Calculate personnel
  const friendlyPersonnel = friendlyUnits.reduce((sum, unit) => sum + unit.personnelCount, 0);
  const enemyPersonnel = enemyUnits.reduce((sum, unit) => sum + unit.personnelCount, 0);
  
  // Calculate equipment by type
  const friendlyEquipmentByType: Record<string, number> = {};
  const enemyEquipmentByType: Record<string, number> = {};
  
  friendlyEquipment.forEach(eq => {
    friendlyEquipmentByType[eq.type] = (friendlyEquipmentByType[eq.type] || 0) + eq.quantity;
  });
  
  enemyEquipment.forEach(eq => {
    enemyEquipmentByType[eq.type] = (enemyEquipmentByType[eq.type] || 0) + eq.quantity;
  });
  
  // Calculate readiness
  const avgFriendlyReadiness = friendlyUnits.reduce((sum, unit) => sum + unit.readiness, 0) / friendlyUnits.length;
  const avgEnemyReadiness = enemyUnits.reduce((sum, unit) => sum + unit.readiness, 0) / enemyUnits.length;

  return {
    friendlyPersonnel,
    enemyPersonnel,
    friendlyEquipmentByType,
    enemyEquipmentByType,
    avgFriendlyReadiness,
    avgEnemyReadiness
  };
}

// Add a unit
export function addUnit(unit: Omit<Unit, 'id' | 'children'>, currentUnits: Unit[]): Unit {
  const newUnit: Unit = {
    id: generateId('unit'),
    ...unit,
    children: []
  };
  currentUnits.push(newUnit);
  return newUnit;
}

// Update a unit
export function updateUnit(updatedUnit: Unit, currentUnits: Unit[]): Unit {
  const index = currentUnits.findIndex(u => u.id === updatedUnit.id);
  if (index !== -1) {
    currentUnits[index] = updatedUnit;
    return updatedUnit;
  }
  throw new Error('Unit not found');
}

// Delete a unit
export function deleteUnit(unitId: string, currentUnits: Unit[], currentEquipment: Equipment[]): boolean {
  const initialLength = currentUnits.length;
  const deleteChildren = (parentId: string) => {
    const children = currentUnits.filter(u => u.parentId === parentId);
    children.forEach(child => {
      deleteChildren(child.id);
      // Delete associated equipment
      currentEquipment = currentEquipment.filter(eq => eq.unitId !== child.id);
    });
    currentUnits = currentUnits.filter(u => u.id !== parentId);
  };

  deleteChildren(unitId);
  // Delete associated equipment for the main unit
  currentEquipment = currentEquipment.filter(eq => eq.unitId !== unitId);
  return currentUnits.length < initialLength;
}

// Add equipment
export function addEquipment(equipment: Omit<Equipment, 'id'>, currentEquipment: Equipment[]): Equipment {
  const newEquipment = {
    id: generateId('eq'),
    ...equipment
  };
  currentEquipment.push(newEquipment);
  return newEquipment;
}

// Update equipment
export function updateEquipment(updatedEquipment: Equipment, currentEquipment: Equipment[]): Equipment {
  const index = currentEquipment.findIndex(eq => eq.id === updatedEquipment.id);
  if (index !== -1) {
    currentEquipment[index] = updatedEquipment;
    return updatedEquipment;
  }
  throw new Error('Equipment not found');
}

// Delete equipment
export function deleteEquipment(equipmentId: string, currentEquipment: Equipment[]): boolean {
  const initialLength = currentEquipment.length;
  currentEquipment = currentEquipment.filter(eq => eq.id !== equipmentId);
  return currentEquipment.length < initialLength;
}

// Add analysis
export function addAnalysis(analysis: Omit<Analysis, 'id'>, currentAnalyses: Analysis[]): Analysis {
  const newAnalysis = {
    id: generateId('a'),
    ...analysis
  };
  currentAnalyses.push(newAnalysis);
  return newAnalysis;
}

// Update analysis
export function updateAnalysis(updatedAnalysis: Analysis, currentAnalyses: Analysis[]): Analysis {
  const index = currentAnalyses.findIndex(a => a.id === updatedAnalysis.id);
  if (index !== -1) {
    currentAnalyses[index] = updatedAnalysis;
    return updatedAnalysis;
  }
  throw new Error('Analysis not found');
}

// Delete analysis
export function deleteAnalysis(analysisId: string, currentAnalyses: Analysis[]): boolean {
  const initialLength = currentAnalyses.length;
  currentAnalyses = currentAnalyses.filter(a => a.id !== analysisId);
  return currentAnalyses.length < initialLength;
}

//  Get AI insights
export function getAIInsights(): string[] {
  return [
    "توقع بشن هجوم مضاد من قبل العدو خلال 48 ساعة القادمة في محور حلب-إدلب.",
    "تحليل أظهر ضعفًا في الدفاعات الجوية للعدو في منطقة البوكمال يمكن استغلاله.",
    "تقدير بأن وحدات العدو في درعا تعاني من نقص حاد في الذخيرة بعد الاشتباكات الأخيرة.",
    "رصد تحركات لوجستية مكثفة تشير إلى نية العدو لتعزيز قواته في تدمر.",
    "تقييم أن القوات الصديقة بحاجة ماسة لإعادة التموين في الجبهة الشمالية الشرقية.",
    "توقع بنجاح عملية الإنزال الجوي بنسبة 80% في حال توفر الدعم الناري الكافي."
  ];
}

// Build unit hierarchy
export function buildUnitHierarchy(allUnits: Unit[], side: 'friendly' | 'enemy' | 'all' = 'all') {
  const rootUnits = allUnits.filter(unit => !unit.parentId && (side === 'all' || unit.side === side));

  const buildHierarchy = (unit: Unit): Unit => {
    const childrenUnits = allUnits.filter(child => child.parentId === unit.id);
    return {
      ...unit,
      children: childrenUnits.map(buildHierarchy)
    };
  };

  return rootUnits.map(buildHierarchy);
}

// Initialize units and equipment
const units = getInitialUnits();
export { units };
export const equipment = getInitialEquipment(units);
 
 