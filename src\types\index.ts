export  interface Unit {
  id: string;
  name: string;
  type: UnitType;
  side: 'friendly' | 'enemy';
  commander: string;
  personnelCount: number;
  parentId: string | null;
  children: Unit[];
  location: {
    type: 'point' | 'polygon';
    coordinates: number[][] | number[][][];
  };
  status: 'active' | 'reserve' | 'damaged';
  equipmentIds: string[];
  readiness: number;
}

export type UnitType = 'division' | 'brigade' | 'battalion' | 'company' | 'platoon' | 'squad';

export const unitTypeHierarchy: UnitType[] = ['division', 'brigade', 'battalion', 'company', 'platoon', 'squad'];

export interface Equipment {
  id: string;
  name: string;
  type: EquipmentType;
  status: 'operational' | 'maintenance' | 'damaged' | 'destroyed';
  quantity: number;
  unitId: string | null;
  capabilities: EquipmentCapability[];
  range?: number; // in kilometers
  accuracy?: number; // percentage
  firepower?: number; // relative scale 1-10
}

export type EquipmentType = 
  'tank' | 
  'apc' | 
  'artillery' | 
  'anti_air' | 
  'helicopter' | 
  'fighter' | 
  'drone' | 
  'missile' | 
  'radar' | 
  'logistics' | 
  'medical' | 
  'command';

export type EquipmentCapability = 
  'attack' | 
  'defense' | 
  'reconnaissance' | 
  'transport' | 
  'medical' | 
  'communication' | 
  'electronic_warfare';

export interface Analysis {
  id: string;
  title: string;
  description: string;
  recommendation: string;
  relatedUnitIds: string[];
  timestamp: string;
  aiConfidence: number;
  threatLevel: 'low' | 'medium' | 'high';
  type: 'offensive' | 'defensive' | 'intelligence' | 'logistical';
}

export interface AITargetingResult {
  targetUnitId: string;
  targetType?: 'unit' | 'equipment';
  bestAttackerUnitId: string;
  recommendedEquipmentIds?: string[];
  recommendedWeapon: string;
  successProbability: number;
  estimatedDamage: number;
  collateralRisk: number;
  collateralDamageRisk?: 'low' | 'medium' | 'high';
  recommendations?: string[];
  friendlyUnitsInRange?: Unit[];
}

export interface BattleSimulationParams {
  attackerUnitIds: string[];
  defenderUnitIds: string[];
  terrain: 'urban' | 'desert' | 'mountain' | 'forest' | 'plain';
  weather: 'clear' | 'rain' | 'snow' | 'fog' | 'sandstorm';
  timeOfDay: 'day' | 'night';
}

export interface BattleSimulationResult {
  winningProbability: number;
  estimatedLosses: {
    personnel: number;
    equipment: Record<EquipmentType, number>;
  };
  duration: number; // hours
  keyFactors: string[];
}
 